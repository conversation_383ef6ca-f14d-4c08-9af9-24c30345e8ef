<Project Sdk="Godot.NET.Sdk/4.5.0-dev.4">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <EnableDynamicLoading>true</EnableDynamicLoading>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="HTTPlease.Core" Version="1.8.9" />
    <PackageReference Include="K4os.Compression.LZ4" Version="1.3.8" />
    <PackageReference Include="K4os.Compression.LZ4.Streams" Version="1.3.8" />
    <PackageReference Include="SpaceWizards.Lidgren.Network" Version="0.3.1" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="src\Prefabs\PlayerHud\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="INVENTORY_SYSTEM.md" />
  </ItemGroup>
</Project>