using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text.Json;
using Godot;
using Lidgren.Network;
using Staga.Archetypes;
using Staga.Archetypes.Server;
using Staga.Archetypes.StagaNet;
using Staga.Entities;
using Staga.Entities.Humanoid;

namespace Staga.Network
{
	public class ServerManager
	{
		public NetServer NetServer { get; private set; }
		public bool IsRunning { get; private set; }
		public uint NextNetworkId { get; set; } = 1;
		private HashSet<NetConnection> _readyConnections = new();
		private Dictionary<NetConnection, Queue<NetOutgoingMessage>> _unreadyMessageQueue = new();

		public ServerConfig? ServerConfig;

		public void StartSession(ushort maxClients)
		{
			try
			{
				string? locationArgument = Util.GetCmdlineArgumentValue<string>("serverconfig");
				string configLocation = locationArgument ?? ProjectSettings.GlobalizePath("res://") + "serverconfig.json";
				Debug.Log($"Loading server configuration at {configLocation}");

				FileAccess file = FileAccess.Open(configLocation, FileAccess.ModeFlags.Read);

				if (file == null)
				{
					Debug.Crash($"Failed to open server configuration file at {configLocation}");
					return;
				}

				ServerConfig = JsonSerializer.Deserialize<ServerConfig>(file.GetAsText());
				if (ServerConfig == null)
				{
					Debug.Crash($"Failed to deserialize server configuration file at {configLocation}");
					return;
				}

				if (ServerConfig.GetGameOption("KeepInventory"))
				{
					Debug.Crash("KEEP INVENTORY DETECTED!!!! LOSER!!!!! THIS IS A NO KEEP INVENTORY HOUSEHOLD!");
				}

				var peerConfig = new NetPeerConfiguration(BuildInfo.Identifier)
				{
					ConnectionTimeout = 8f,
					MaximumConnections = maxClients,
					AutoExpandMTU = true,
					Port = ServerConfig.ServerPort,
					EnableUPnP = ServerConfig.EnableUPnP,
					LocalAddress = IPAddress.Parse(ServerConfig.ServerHost),
				};
				peerConfig.EnableMessageType(NetIncomingMessageType.ConnectionApproval);

				NetServer = new NetServer(peerConfig);
				NetServer.Start();
				IsRunning = true;

				Debug.Log($"Server started on 0.0.0.0:{ServerConfig.ServerPort}");
				if (ServerConfig.EnableUPnP)
					NetServer.UPnP?.ForwardPort(ServerConfig.ServerPort, "STAGA Game server");

				Http.Token = ServerConfig.ServerToken;
				
				Collections.NetworkManager.ChatManager.ServerCreateChannel(ChatManager.LocalChannelId, "Local");
				if (ServerConfig.GetGameOption("ShoutcastTextChannel"))
					Collections.NetworkManager.ChatManager.ServerCreateChannel(ChatManager.ShoutcastChannelId,
						"Shoutcast");

				DisplayServer.WindowSetVsyncMode(DisplayServer.VSyncMode.Disabled);
				Engine.SetPhysicsTicksPerSecond(ServerConfig.ServerTickRate);
				
				Collections.Util.GetTree().ChangeSceneToFile("res://scenes/map.tscn");
			}
			catch (Exception ex)
			{
				Debug.Error(ex.ToString());
				Debug.Crash($"Server initialization failed: {ex.Message}");
			}
		}

		public void StopSession()
		{
			NetServer.Shutdown("Server shutting down, bye!");
			IsRunning = false;
		}

		public void Update()
		{
			NetIncomingMessage msg;
			while ((msg = NetServer.ReadMessage()) != null)
			{
				switch (msg.MessageType)
				{
					case NetIncomingMessageType.StatusChanged:
						HandleStatusChanged(msg);
						break;

					case NetIncomingMessageType.ConnectionApproval:
						Debug.Log(
							$"Client {NetUtility.ToHexString(msg.SenderConnection.RemoteUniqueIdentifier)} ({msg.SenderConnection.RemoteEndPoint.Address}) connecting");
						HandleConnectionApproval(msg);
						break;

					case NetIncomingMessageType.Data:
						HandleMessage(msg);
						break;

					case NetIncomingMessageType.DebugMessage:
						Debug.Log(msg.ReadString(), "Lidgren", "Lidgren");
						break;

					case NetIncomingMessageType.WarningMessage:
						Debug.Warn(msg.ReadString(), "Lidgren", "Lidgren");
						break;
				}

				NetServer.Recycle(msg);
			}

			NetServer.FlushSendQueue();
		}

		private void HandleMessage(NetIncomingMessage msg)
		{
			if (msg.SenderConnection == null)
			{
				Debug.Error("Received message with null sender connection");
				return;
			}

			try
			{
				var messageId = (NetworkManager.MessageId)msg.ReadByte();

				if (msg.SenderConnection == null)
				{
					Debug.Error("Received message with null sender connection");
					return;
				}

				switch (messageId)
				{
					case NetworkManager.MessageId.Ready:
						_readyConnections.Add(msg.SenderConnection);
						SendInitialGameState(msg.SenderConnection);
						break;

					case NetworkManager.MessageId.EntityState:
					case NetworkManager.MessageId.EntityCreate:
					case NetworkManager.MessageId.EntityDestroy:
					case NetworkManager.MessageId.EntityEvent:
						Collections.CurrentGameWorld?.HandleEntityMessage(messageId, msg);
						break;

					case NetworkManager.MessageId.EntityStateCorrectionAck:
						Collections.CurrentGameWorld?.HandleCorrectionAck(msg);
						break;

					case NetworkManager.MessageId.TextChannelJoin:
					case NetworkManager.MessageId.TextChannelLeave:
					case NetworkManager.MessageId.TextChannelMessage:
						Collections.NetworkManager.ChatManager.HandleNetworkMessage(messageId, msg);
						break;

					case NetworkManager.MessageId.PlayerListFullSync:
					case NetworkManager.MessageId.PlayerJoin:
					case NetworkManager.MessageId.PlayerLeave:
						Collections.CurrentGameWorld?.HandlePlayerState(messageId, msg);
						break;
					case NetworkManager.MessageId.InventorySync:
					case NetworkManager.MessageId.InventoryAdd:
					case NetworkManager.MessageId.InventoryRemove:
					case NetworkManager.MessageId.InventorySwitchIntent:
					case NetworkManager.MessageId.InventoryUseIntent:
					case NetworkManager.MessageId.InventoryUseResponse:
						Collections.CurrentGameWorld?.HandleInventoryMessage(messageId, msg);
						break;

					default:
						Debug.Warn(
							$"Unhandled message type from client {msg.SenderConnection.RemoteUniqueIdentifier}: {messageId}");
						break;
				}
			}
			catch (Exception ex)
			{
				Debug.Error(
					$"Error handling message from client {NetUtility.ToHexString(msg.SenderConnection.RemoteUniqueIdentifier)}: {ex}");
				Kick(msg.SenderConnection.RemoteUniqueIdentifier, "Internal server error processing message");
			}
		}

		private void SendInitialGameState(NetConnection connection)
		{
			try
			{
				var playerListMsg = NetServer.CreateMessage();
				playerListMsg.Write((byte)NetworkManager.MessageId.PlayerListFullSync);
				NetServer.SendMessage(playerListMsg, connection, NetDeliveryMethod.ReliableUnordered);

				if (Collections.CurrentGameWorld != null)
				{
					foreach (var entity in Collections.CurrentGameWorld.Entities.Values)
					{
						var entityMsg = NetServer.CreateMessage();
						entityMsg.Write((byte)NetworkManager.MessageId.EntityCreate);
						entityMsg.Write(entity.GetType().Name);
						entityMsg.Write(entity.NetworkId);
						entityMsg.Write(entity.OwnerId);

						NetServer.SendMessage(entityMsg, connection, NetDeliveryMethod.ReliableUnordered);

						// Send inventory sync for inventory entities
						if (entity is BaseHumanoid humanoid)
						{
							var inventoryData = humanoid.CreateInventorySyncData();
							var inventoryBytes = inventoryData.Serialize();

							var inventoryMsg = NetServer.CreateMessage();
							inventoryMsg.Write((byte)NetworkManager.MessageId.InventorySync);
							inventoryMsg.Write(inventoryBytes.Length);
							inventoryMsg.Write(inventoryBytes);

							NetServer.SendMessage(inventoryMsg, connection, NetDeliveryMethod.ReliableUnordered);
						}
					}
				}
				else
					throw new Exception("CurrentGameWorld is null");

				Collections.NetworkManager.ChatManager.ServerAddListener(ChatManager.LocalChannelId,
					connection.RemoteUniqueIdentifier);
				Collections.NetworkManager.ChatManager.ServerAddListener(ChatManager.ShoutcastChannelId,
					connection.RemoteUniqueIdentifier);

				if (_unreadyMessageQueue.TryGetValue(connection, out var queue))
				{
					Debug.Log(
						$"Sending queued messages to client {NetUtility.ToHexString(connection.RemoteUniqueIdentifier)}");
					while (queue.Count > 0)
					{
						var msg = queue.Dequeue();
						NetServer.SendMessage(msg, connection, NetDeliveryMethod.ReliableUnordered);
					}

					_unreadyMessageQueue.Remove(connection);
				}

				Debug.Log(
					$"Sent initial game state to client {NetUtility.ToHexString(connection.RemoteUniqueIdentifier)}");
			}
			catch (Exception ex)
			{
				Debug.Error(
					$"Error sending initial game state to client {NetUtility.ToHexString(connection.RemoteUniqueIdentifier)}: {ex}");
				connection.Disconnect("Failed to send initial game state");
			}
		}

		private void HandleStatusChanged(NetIncomingMessage msg)
		{
			var status = (NetConnectionStatus)msg.ReadByte();

			switch (status)
			{
				case NetConnectionStatus.Connected:
					OnClientConnected(msg.SenderConnection);
					break;

				case NetConnectionStatus.Disconnected:
					OnClientDisconnected(msg.SenderConnection);
					break;
			}
		}

		private async void HandleConnectionApproval(NetIncomingMessage msg)
		{
			if (msg.SenderConnection == null)
			{
				Debug.Error("Connection approval message received with null sender connection");
				return;
			}

			try
			{
				var connection = msg.SenderConnection;
				var version = msg.ReadDouble();
				var pvn = msg.ReadUInt32();
				var characterId = msg.ReadUInt32();
				var sessionToken = msg.ReadString();

				if (pvn < BuildInfo.ProtocolVersion)
				{
					Debug.Warn(
						$"Disconnecting client {NetUtility.ToHexString(connection.RemoteUniqueIdentifier)} due to outdated protocol version");
					connection.Deny(
						$"Your game client is outdated. Please update through the launcher. (Expected: {BuildInfo.ProtocolVersion}, Got: {pvn})");
					return;
				}

				if (string.IsNullOrWhiteSpace(sessionToken))
				{
					Debug.Warn(
						$"Disconnecting client {NetUtility.ToHexString(connection.RemoteUniqueIdentifier)} due to malformed handshake message");
					connection.Deny("Malformed handshake message.");
					return;
				}

				try
				{
					var session = await Collections.Http.FetchAsync<SessionVerify>(
						$"accounts/session/verify/{sessionToken}",
						HttpMethod.Get);

					if (!session.Valid)
					{
						Debug.Warn(
							$"Disconnecting client {NetUtility.ToHexString(connection.RemoteUniqueIdentifier)} due to invalid session");
						connection.Deny("Invalid session, please restart the game client from the launcher.");
						return;
					}

					var account = await Collections.Http.FetchAsync<Account>(
						$"accounts/get/{session.AccountId}",
						HttpMethod.Get);

					if (account.Banned != null)
					{
						Debug.Warn(
							$"Disconnecting client {NetUtility.ToHexString(connection.RemoteUniqueIdentifier)} due to banned account");
						connection.Deny(
							"Your stagaNet account is suspended. Visit [url='https://staganet.misleadingname.cc/banned']this page[/url] for more info.");
						return;
					}
				}
				catch (Exception httpEx)
				{
					Debug.Error($"HTTP error during connection approval: {httpEx}");
					connection.Deny("Failed to verify session. Please try again later.");
					return;
				}

				Debug.Log($"Approved connection from {NetUtility.ToHexString(connection.RemoteUniqueIdentifier)}");
				connection.Approve();

				
				if (ServerConfig == null)
				{
					Debug.Crash("ServerConfig is null");
					return;
				}
				
				await Collections.Util.ToSignal(Collections.Util.GetTree().CreateTimer(0.25f), Timer.SignalName.Timeout);
				
				var meta = NetServer.CreateMessage();
				meta.Write((byte)NetworkManager.MessageId.ServerMeta);
				meta.Write(ServerConfig.ServerName);
				meta.Write(ServerConfig.ServerTickRate);
				
				Debug.Log("Sending server meta");
				NetServer.SendMessage(meta, connection, NetDeliveryMethod.ReliableOrdered);
			}
			catch (Exception e)
			{
				Debug.Error($"Error during connection approval: {e}");
				msg.SenderConnection?.Deny("Internal server error during connection approval");
			}
		}

		private void OnClientConnected(NetConnection connection)
		{
			var identifier = connection.RemoteUniqueIdentifier;

			Collections.CurrentGameWorld?.HandlePlayerConnect(identifier);
		}

		private void OnClientDisconnected(NetConnection connection)
		{
			var identifier = connection.RemoteUniqueIdentifier;

			try
			{
				Collections.CurrentGameWorld?.HandlePlayerDisconnect(identifier);
				_readyConnections.Remove(connection);
			}
			catch (Exception ex)
			{
				Debug.Error($"Error handling client disconnect for {NetUtility.ToHexString(identifier)}: {ex.Message}");
			}

			foreach (var channel in Collections.NetworkManager.ChatManager.Channels.Values)
			{
				Collections.NetworkManager.ChatManager.ServerRemoveListener(channel.Id, identifier);
			}

			Debug.Log($"Client disconnected: {NetUtility.ToHexString(identifier)}");
		}

		public void Kick(long identifier, string reason)
		{
			try
			{
				var connection = GetConnection(identifier);
				connection?.Disconnect($"Kicked: {reason}");
				Debug.Log($"Kicked client {NetUtility.ToHexString(identifier)}: {reason}");
			}
			catch (Exception e)
			{
				Debug.Error($"Error kicking client {identifier}: {e.Message}");
			}
		}

		public void Kick(NetConnection connection, string reason) => Kick(connection.RemoteUniqueIdentifier, reason);

		public bool HasClient(long identifier)
		{
			return NetServer.Connections.Any(c => c.RemoteUniqueIdentifier == identifier);
		}

		public NetConnection? GetConnection(long identifier)
		{
			if (!HasClient(identifier))
			{
				Debug.Warn(
					$"Attempted to get connection for non-existent client: {NetUtility.ToHexString(identifier)}");
				return null;
			}

			return NetServer.Connections.Find(c => c.RemoteUniqueIdentifier == identifier);
		}

		public void SendToAll(NetOutgoingMessage msg, NetDeliveryMethod method, NetConnection[] exclude = null,
			bool queueForUnready = false)
		{
			foreach (NetConnection connection in NetServer.Connections)
			{
				if (queueForUnready && !_readyConnections.Contains(connection))
				{
					if (!_unreadyMessageQueue.TryGetValue(connection, out var queue))
						_unreadyMessageQueue[connection] = queue = new Queue<NetOutgoingMessage>();

					Debug.Log(
						$"Queued message for unready client {NetUtility.ToHexString(connection.RemoteUniqueIdentifier)}");
					queue.Enqueue(msg);
					continue;
				}

				if (exclude != null && exclude.Contains(connection))
					continue;

				if (!_readyConnections.Contains(connection))
					continue;

				NetServer.SendMessage(msg, connection, method);
			}
		}
	}
}