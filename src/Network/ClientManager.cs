using System;
using System.Net.Http;
using Godot;
using Lidgren.Network;
using Staga.Archetypes;
using staga.Archetypes.Client;
using Staga.Archetypes.Server;
using Staga.Archetypes.StagaNet;
using Staga.Prefabs.PopupBoxes;

namespace Staga.Network
{
	public class ClientManager
	{
		public NetClient NetClient;
		public bool IsConnected { get; private set; }
		public float RoundTripTime => NetClient?.ServerConnection?.AverageRoundtripTime ?? 0f;

		public string LastServerIp;
		public ushort LastServerPort;

		public string SessionToken;
		public uint SelectedCharacterId;
		internal bool ClientInitiatedDisconnect = false;
		internal ServerMeta? ServerMeta;

		public async void ConnectSession(string host, ushort port)
		{
			if (NetClient != null &&
			    (NetClient.ConnectionStatus != NetConnectionStatus.Disconnected ||
			     NetClient.ConnectionStatus != NetConnectionStatus.None) && IsConnected)
				return;

			Collections.NetworkManager.ChatManager.CleanUp();

			Collections.Util.ToggleLoadingScreen(true);
			Collections.Util.LoadingStatus = "Generating Session data";

			var config = new NetPeerConfiguration(BuildInfo.Identifier)
			{
				MaximumConnections = 1,
				AutoExpandMTU = true,
				EnableUPnP = false,
				ConnectionTimeout = 8f,
				Port = 0, // Use 0 to let the system assign a random available port for the client
			};
			config.EnableMessageType(NetIncomingMessageType.ConnectionApproval);

			NetClient = new NetClient(config);
			NetClient.Start();

			try
			{
				var session = await Collections.Http.FetchAsync<Session>("/auth/me/session/start/", HttpMethod.Post);
				SessionToken = session.Token;

				var hailMessage = NetClient.CreateMessage();
				hailMessage.Write(BuildInfo.Version);
				hailMessage.Write(BuildInfo.ProtocolVersion);
				hailMessage.Write(SelectedCharacterId);
				hailMessage.Write(SessionToken);

				Debug.Log("Generated hail message");

				Collections.Util.LoadingStatus = "Connecting to server";

				Debug.Log($"Connecting to {host}:{port}");
				NetClient.Connect(host, port, hailMessage);
				IsConnected = true;

				LastServerIp = host;
				LastServerPort = port;

				Debug.Log("Sent hail message");
			}
			catch (Exception e)
			{
				Debug.Crash(e.Message);
				Debug.Error(e.ToString());
			}
		}

		public void Update()
		{
			NetIncomingMessage msg;
			while ((msg = NetClient.ReadMessage()) != null)
			{
				try
				{
					switch (msg.MessageType)
					{
						case NetIncomingMessageType.StatusChanged:
							HandleStatusChanged(msg);
							break;

						case NetIncomingMessageType.Data:
							HandleMessage(msg);
							break;

						case NetIncomingMessageType.DebugMessage:
							Debug.Log(msg.ReadString(), "Lidgren");
							break;

						case NetIncomingMessageType.WarningMessage:
							Debug.Warn(msg.ReadString(), "Lidgren");
							break;

						case NetIncomingMessageType.ErrorMessage:
							Debug.Error(msg.ReadString(), "Lidgren");
							break;
					}
				}
				finally
				{
					NetClient.Recycle(msg);
				}
			}

			NetClient.FlushSendQueue();
		}

		private void HandleMessage(NetIncomingMessage msg)
		{
			var messageId = (NetworkManager.MessageId)msg.ReadByte();
			
			if(msg.SenderConnection == null)
			{
				Debug.Error("Received message with null sender connection");
				return;
			}

			switch (messageId)
			{
				case NetworkManager.MessageId.ServerMeta:
					Debug.Log("Received server meta");
					ServerMeta = new()
					{
						Name = msg.ReadString(),
						TickRate = msg.ReadUInt16()
					};
					break;
				
				case NetworkManager.MessageId.EntityCreate:
				case NetworkManager.MessageId.EntityDestroy:
				case NetworkManager.MessageId.EntityState:
				case NetworkManager.MessageId.EntityEvent:
				case NetworkManager.MessageId.EntityStateCorrection:
					if (Collections.CurrentGameWorld != null)
						Collections.CurrentGameWorld.HandleEntityMessage(messageId, msg);
					break;

				case NetworkManager.MessageId.TextChannelJoin:
				case NetworkManager.MessageId.TextChannelLeave:
				case NetworkManager.MessageId.TextChannelMessage:
					Collections.NetworkManager.ChatManager.HandleNetworkMessage(messageId, msg);
					break;

				case NetworkManager.MessageId.PlayerListFullSync:
				case NetworkManager.MessageId.PlayerJoin:
				case NetworkManager.MessageId.PlayerLeave:
					if (Collections.CurrentGameWorld != null)
						Collections.CurrentGameWorld.HandlePlayerState(messageId, msg);
					break;

				case NetworkManager.MessageId.InventorySync:
				case NetworkManager.MessageId.InventoryAdd:
				case NetworkManager.MessageId.InventoryRemove:
				case NetworkManager.MessageId.InventorySwitchResponse:
				case NetworkManager.MessageId.InventoryUseResponse:
					if (Collections.CurrentGameWorld != null)
						Collections.CurrentGameWorld.HandleInventoryMessage(messageId, msg);
					break;

				case NetworkManager.MessageId.Disconnect:
					var reason = msg.ReadString();
					NetClient.Disconnect(reason);
					break;

				default:
					Debug.Warn($"Unhandled message type: {messageId}");
					break;
			}
		}

		private void HandleStatusChanged(NetIncomingMessage msg)
		{
			var status = (NetConnectionStatus)msg.ReadByte();
			var reason = msg.ReadString();

			switch (status)
			{
				case NetConnectionStatus.Connected:
					OnConnected();
					break;

				case NetConnectionStatus.Disconnected:
					OnDisconnected(reason);
					break;

				case NetConnectionStatus.None:
					OnConnectionFailed();
					break;
			}
		}

		private async void OnConnected()
		{
			Collections.Util.LoadingStatus = "Transcending";

			await Collections.Util.ChangeScene("map.tscn");
			Collections.Util.ToggleLoadingScreen(false);
		}

		private void OnDisconnected(string reason)
		{
			Collections.Util.ToggleLoadingScreen(false);

			ServerMeta = null;
			IsConnected = false;

			Debug.Log($"Disconnected from server: {reason}");
			Collections.NetworkManager.ChatManager.CleanUp();

			NetClient.Shutdown(reason);

			if (ClientInitiatedDisconnect)
			{
				Collections.NetworkManager.GetTree().ChangeSceneToFile("res://scenes/menu.tscn");
				ClientInitiatedDisconnect = false;
				return;
			}

			Debug.Warn("Non client initiated disconnect");

			MessageBox box = Util.MessageBox();
			Collections.Util.GetTree().Root.AddChild(box);
			
			box.AddButton("Retry", Callable.From(() => { ConnectSession(LastServerIp, LastServerPort); }));
			box.AddButton("OK",
				Callable.From(() =>
				{
					Collections.NetworkManager.GetTree().ChangeSceneToFile("res://scenes/menu.tscn");
				}));

			box.PopupMessage("Disconnected from session", reason);
		}

		private void OnConnectionFailed()
		{
			Debug.Log("Connection failed, host error");
			Collections.Util.ToggleLoadingScreen(false);

			ServerMeta = null;
			IsConnected = false;
			Collections.NetworkManager.GetTree().ChangeSceneToFile("res://scenes/menu.tscn");
			Collections.NetworkManager.ChatManager.CleanUp();

			MessageBox box = Util.MessageBox();
			Collections.Util.GetTree().Root.AddChild(box);

			box.AddButton("Retry", Callable.From(() => { ConnectSession(LastServerIp, LastServerPort); }));
			box.AddButton("OK",
				Callable.From(() =>
				{
					Collections.NetworkManager.GetTree().ChangeSceneToFile("res://scenes/menu.tscn");
				}));

			box.PopupMessage("Connection to session failed", "Failed to connect to session, check the host.");
		}

		public void Disconnect()
		{
			ClientInitiatedDisconnect = true;
			NetClient.Disconnect("Client disconnected");
		}

		public void Dispose()
		{
			Disconnect();
			NetClient = null;
		}
	}
}