using Godot;
using Lidgren.Network;

namespace Staga.Network
{
	public partial class NetworkManager : Node
	{
		public const ushort DefaultPort = 13320;
		public double TickRate
		{
			get
			{
				if (IsServerMode)
					return 1.0 / (int)ProjectSettings.GetSetting("physics/common/physics_ticks_per_second");
				if (IsClientMode)
					return 1.0 / Client.ServerMeta.TickRate;
				return 0f;
			}
		}

		public ClientManager Client { get; private set; }
		public ServerManager Server { get; private set; }
		public ChatManager ChatManager { get; private set; }
		
		public enum MessageId : byte
		{
			Disconnect,
			ServerMeta,
			Ready,

			TextChannelJoin,
			TextChannelLeave,
			TextChannelMessage,

			PlayerListFullSync,
			PlayerJoin,
			PlayerLeave,

			EntityCreate,
			EntityDestroy,
			EntityState,
			EntityStateCorrection,
			EntityStateCorrectionAck,
			EntityEvent,

			InventorySync,
			InventoryAdd,
			InventoryRemove,
			InventorySwitchIntent,
			InventorySwitchResponse,
			InventoryUseIntent,
			InventoryUseResponse,

			HealthChanged,
			StandingKnocked
		}

		public bool IsServerMode => Server.NetServer != null && Server.IsRunning;
		public bool IsClientMode => Client.NetClient != null && Client.IsConnected;
		
		public override void _Ready()
		{
			Collections.NetworkManager = this;

			Client = new();
			Server = new();
			ChatManager = new();

			AddChild(ChatManager);
		}

		public override void _ExitTree()
		{
			Client.Dispose();
			Server.StopSession();
		}

		public override void _Process(double delta)
		{
			if (IsClientMode)
				Client.Update();
			if (IsServerMode)
				Server.Update();

			if (Input.IsActionJustPressed("PanicDisconnect") && IsClientMode)
			{
				Collections.Util.InfoBox("Panic Disconnect", "You've just hit the panic disconnect keybind (Ctrl + Shift + Alt + Delete). This forcefully disconnected you from the server.\n\n[b]Your character is lingering for 30 seconds until the server times you out![/b]");
				Debug.Log("Panic disconnect triggered.");
				Client.Dispose();
				
				Collections.Util.GetTree().ChangeSceneToFile("res://scenes/menu.tscn");
			}
		}
	}
}