using Godot;

namespace Staga.Entities.Humanoid;

public partial class Player : BaseHumanoid
{
	private const float WalkSpeed = 5.5f;
	private const float RunSpeed = 8.0f;
	private const float JumpPower = 19.0f;
	private const float Acceleration = 24.0f;
	private const float Gravity = 89.0f;

	private SpringArm3D _arm;
	private Camera3D _camera;
	private Node3D _nametag;
	private BoneAttachment3D _headAttachment;

	private bool _lookingMouse;
	private Vector2 _lookingMousePos;
	private bool _isShiftLocked;

	public enum PlayerEvents : byte
	{
		Jump
	}

	public override void OnCreate()
	{
		base.OnCreate();
		Debug.Log($"Player {NetworkId} created");

		_arm = GetNode<SpringArm3D>("CameraArm");
		_camera = _arm.GetNode<Camera3D>("Camera3D");
		_headAttachment = GetNode<BoneAttachment3D>("HeadAttachment");
		_nametag = _headAttachment.GetNode<Node3D>("Nametag");

		if (!IsOwner) return;

		_camera.Current = true;
		_nametag.Visible = false;
	}

	public override void OnDestroy()
	{
		base.OnDestroy();
		Debug.Log($"Player {NetworkId} destroyed");
	}

	public override void ServerUpdate(double delta)
	{
		ApplyMovement(delta);

		base.ServerUpdate(delta);
	}

	public override void ClientUpdate(double delta)
	{
		if (IsOwner)
			ApplyMovement(delta);

		AnimTree.Set("parameters/FallBlend/blend_amount", IsOnFloor() ? 0.0f : 1.0f);
		
		base.ClientUpdate(delta);
	}

	public override void ProcessClientEvent(byte eventType, byte[] args)
	{
		PlayerEvents ev = (PlayerEvents)eventType;

		switch (ev)
		{
			case PlayerEvents.Jump:
				JumpSound.Play();
				AnimTree.Set("parameters/JumpOneShot/request", (int)AnimationNodeOneShot.OneShotRequest.Fire);
				break;
		}
	}

	public override void ProcessServerEvent(byte eventType, byte[] args)
	{
		PlayerEvents ev = (PlayerEvents)eventType;
		
		switch (ev)
		{
			case PlayerEvents.Jump:
				SendEntityEvent(eventType, args, excludeOwner: true);
				break;
		}
	}

	#region Movement and Input

	private void ApplyMovement(double delta)
	{
		Vector3 vel = Velocity;
		Vector3 forward = new Vector3(-Mathf.Sin(_arm.Rotation.Y), 0, -Mathf.Cos(_arm.Rotation.Y));
		Vector3 strafe = new Vector3(Mathf.Sin(_arm.Rotation.Y - Mathf.Pi * 0.5f), 0,
			Mathf.Cos(_arm.Rotation.Y - Mathf.Pi * 0.5f));

		float forwardInput = 0.0f;
		float strafeInput = 0.0f;

		if (Collections.Util.GameplayInputEnabled)
		{
			forwardInput = Input.GetActionStrength("MovementForward") - Input.GetActionStrength("MovementBackward");
			strafeInput = Input.GetActionStrength("MovementLeft") - Input.GetActionStrength("MovementRight");
		}

		Vector3 wishDir = (forward * forwardInput + strafe * strafeInput).Normalized();

		if (IsOnFloor())
		{
			if (Input.IsActionPressed("MovementJump") && Collections.Util.GameplayInputEnabled)
			{
				vel.Y = JumpPower;
				SendEntityEvent((byte)PlayerEvents.Jump, [], callLocal: true);
			}
			else
				vel.Y = 0.0f;
		}
		else
		{
			vel.Y -= Gravity * (float)delta;
		}

		if (!wishDir.IsZeroApprox())
		{
			float targetAngle = -Mathf.Atan2(wishDir.X, -wishDir.Z);
			float currentAngle = Avatar.Rotation.Y;
			float rotationSpeed = 12.0f;

			float newAngle = Mathf.LerpAngle(
				currentAngle,
				targetAngle,
				1.0f - Mathf.Exp(-rotationSpeed * (float)delta)
			);

			Avatar.Rotation = Vector3.Up * newAngle;
		}

		Vector3 targetVel = wishDir * WalkSpeed;

		Vector3 horizontalVel = new Vector3(vel.X, 0, vel.Z);
		Vector3 newHorizontalVel = horizontalVel.Lerp(
			targetVel,
			1.0f - Mathf.Exp(-Acceleration * (float)delta)
		);

		vel = new Vector3(newHorizontalVel.X, vel.Y, newHorizontalVel.Z);

		Velocity = vel;
	}

	public override void _UnhandledInput(InputEvent @event)
	{
		if (@event is InputEventMouseMotion evMotion && _lookingMouse)
		{
			float mouseSens = SettingsManager.Settings.MouseSensitivity;

			_arm.RotationDegrees += new Vector3(evMotion.ScreenRelative.Y * -mouseSens, evMotion.ScreenRelative.X * -mouseSens, 0);
			_arm.RotationDegrees = _arm.RotationDegrees with { X = float.Clamp(_arm.RotationDegrees.X, -90, 90) };
		}

		if (@event is InputEventMouseButton evButton)
		{
			if (evButton.ButtonIndex != MouseButton.Right) return;

			Input.MouseMode =
				evButton.Pressed ? Input.MouseModeEnum.Captured : Input.MouseModeEnum.Visible;
			_lookingMouse = evButton.Pressed;
			_lookingMousePos = GetViewport().GetMousePosition();
		}

		if (@event.IsActionPressed("MovementShiftLock"))
		{
			_isShiftLocked = !_isShiftLocked;
		}
	}

	#endregion
}