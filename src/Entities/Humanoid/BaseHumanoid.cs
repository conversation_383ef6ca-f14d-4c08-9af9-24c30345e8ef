using Godot;
using System;
using System.IO;
using System.Collections.Generic;
using Lidgren.Network;
using Staga.Network;
using Staga.Scenes;

namespace Staga.Entities.Humanoid;

/// <summary>
/// Base class for humanoid entities that implements network synchronization, living entity mechanics, and inventory management
/// </summary>
public partial class BaseHumanoid : CharacterBody3D, ILivingEntity, IInventoryEntity
{
	public uint NetworkId { get; set; }
	public long OwnerId { get; set; }

	public bool IsOwner => Collections.CurrentGameWorld!.EntityOwned(this);

	public bool Standing { get; set; }
	public uint Health { get; set; }

	public uint BaseMaxHealth { get; init; } = 100;
	public uint MaxHealth { get; set; }
	public Vector3 VelocityNoY => new(Velocity.X, 0, Velocity.Z);
	
	protected Node3D Avatar;
	protected AnimationTree AnimTree;

	protected AudioStreamPlayer3D JumpSound;
	protected AudioStreamPlayer3D WalkSound;

	protected Interpolator PositionInterpolator;
	protected Interpolator RotationInterpolator;
	protected Interpolator VelocityInterpolator;

	protected Node3D RightHandAttachment;
	protected Node3D ItemAnchor;

	public virtual void OnCreate()
	{
		Avatar = GetNode<Node3D>("Avatar");
		AnimTree = GetNode<AnimationTree>("AnimationTree");

		PositionInterpolator = GetNode<Interpolator>("PositionInterpolator");
		RotationInterpolator = GetNode<Interpolator>("RotationInterpolator");
		VelocityInterpolator = GetNode<Interpolator>("VelocityInterpolator");

		JumpSound = GetNode<AudioStreamPlayer3D>("TorsoAttachment/JumpSound");
		WalkSound = GetNode<AudioStreamPlayer3D>("TorsoAttachment/WalkSound");

		PositionInterpolator.SetPhysicsProcess(!IsOwner);
		RotationInterpolator.SetPhysicsProcess(!IsOwner);
		VelocityInterpolator.SetPhysicsProcess(!IsOwner);

		PositionInterpolator.TimeToReachTarget = 60f * (float)Collections.NetworkManager.TickRate;
		RotationInterpolator.TimeToReachTarget = 60f * (float)Collections.NetworkManager.TickRate;
		VelocityInterpolator.TimeToReachTarget = 60f * (float)Collections.NetworkManager.TickRate;

		RightHandAttachment = GetNode<Node3D>("RightHandAttachment");
		ItemAnchor = RightHandAttachment.GetNode<Node3D>("ItemAnchor");
	}

	public virtual void OnDestroy()
	{
	}

	public virtual void ServerUpdate(double delta)
	{
		MoveAndSlide();
	}

	public virtual void ClientUpdate(double delta)
	{
		WalkSound.StreamPaused = !(!VelocityNoY.IsZeroApprox() && IsOnFloor());

		if (!IsOwner) return;
		MoveAndSlide();
	}

	#region Network State Management

	public virtual byte[] SerializeState()
	{
		using var memory = new MemoryStream();
		using var writer = new BinaryWriter(memory);

		// Base entity state
		writer.Write(Position.X);
		writer.Write(Position.Y);
		writer.Write(Position.Z);

		writer.Write(Velocity.X);
		writer.Write(Velocity.Y);
		writer.Write(Velocity.Z);

		writer.Write(Avatar.Rotation.X);
		writer.Write(Avatar.Rotation.Y);
		writer.Write(Avatar.Rotation.Z);

		// Living entity state
		if (Collections.NetworkManager.IsServerMode) // Server-side only
		{
			writer.Write(true); // Yes, contains living entity state
			writer.Write(Health);
			writer.Write(MaxHealth);
			writer.Write(Standing);
		}
		else
		{
			writer.Write(false); // No, does not contain living entity state
		}

		return memory.ToArray();
	}

	public virtual void DeserializeState(byte[] data)
	{
		using var memory = new MemoryStream(data);
		using var reader = new BinaryReader(memory);

		// Base entity state
		var position = new Vector3(
			reader.ReadSingle(),
			reader.ReadSingle(),
			reader.ReadSingle()
		);
		var velocity = new Vector3(
			reader.ReadSingle(),
			reader.ReadSingle(),
			reader.ReadSingle()
		);

		var rotation = new Vector3(
			reader.ReadSingle(),
			reader.ReadSingle(),
			reader.ReadSingle()
		);
		
		PositionInterpolator.NewUpdate(position, IsOwner);
		VelocityInterpolator.NewUpdate(velocity, IsOwner);
		RotationInterpolator.NewUpdate(rotation, IsOwner);

		if (reader.ReadBoolean()) // Does the state contain living entity data?
		{
			// Yes, deserialize it
			Health = reader.ReadUInt32();
			MaxHealth = reader.ReadUInt32();
			Standing = reader.ReadBoolean();
		} // Or no, and fuck off
	}

	public virtual bool ValidateState(byte[] data)
	{
		try
		{
			using var memory = new MemoryStream(data);
			using var reader = new BinaryReader(memory);

			var position = new Vector3(
				reader.ReadSingle(),
				reader.ReadSingle(),
				reader.ReadSingle()
			);

			var velocity = new Vector3(
				reader.ReadSingle(),
				reader.ReadSingle(),
				reader.ReadSingle()
			);

			// Basic validation
			if (position.Length() > 1000f) return false;
			if (velocity.Length() > 50f) return false;

			// Skip rotation validation
			reader.ReadSingle();
			reader.ReadSingle();
			reader.ReadSingle();

			// raycast position validation
			PhysicsRayQueryParameters3D rayParams = new()
			{
				From = Position + Vector3.Up,
				To = position + Vector3.Up,
				CollisionMask = 1 << 1,
			};
			var raycast = GetWorld3D().DirectSpaceState.IntersectRay(rayParams);
			if (raycast.Count != 0) return false;

			return true;
		}
		catch
		{
			return false;
		}
	}

	public virtual void ProcessClientEvent(byte eventType, byte[] args)
	{
	}

	public virtual void ProcessServerEvent(byte eventType, byte[] args)
	{
	}

	protected void SendEntityEvent(byte eventType, byte[] args, NetDeliveryMethod deliveryMethod = NetDeliveryMethod.UnreliableSequenced, bool callLocal = false, bool excludeOwner = true)
	{
		var msg
			= Collections.NetworkManager.IsClientMode
				? Collections.NetworkManager.Client.NetClient.CreateMessage()
				: Collections.NetworkManager.Server.NetServer.CreateMessage();

		msg.Write((byte)NetworkManager.MessageId.EntityEvent);
		msg.Write(NetworkId);
		msg.Write(eventType);
		msg.Write(args.Length);
		msg.Write(args);

		if (callLocal)
			if (Collections.NetworkManager.IsServerMode)
				ProcessServerEvent(eventType, args);
			else
				ProcessClientEvent(eventType, args);
		
		if (Collections.NetworkManager.IsClientMode)
			Collections.NetworkManager.Client.NetClient.SendMessage(msg, deliveryMethod);
		else
		{
			NetConnection[] exclude = [];
			if (excludeOwner) exclude = [Collections.NetworkManager.Server.GetConnection(OwnerId)];
			
			Collections.NetworkManager.Server.SendToAll(msg, deliveryMethod, exclude);
		}
	}

	#endregion

	#region Living Entity Implementation

	public virtual void ApplyDamage(int amount, uint? sourceEntityId)
	{
		if (!Collections.NetworkManager.IsServerMode) return;
		Health = uint.Max(0, (uint)(Health - amount));
	}

	public virtual void ApplyHealing(int amount, bool bypassLimit)
	{
		if (!Collections.NetworkManager.IsServerMode) return;
		Health = uint.Min(bypassLimit ? MaxHealth : uint.MaxValue, (uint)(Health + amount));
	}

	public void OnDamage()
	{
		throw new NotImplementedException();
	}

	public void OnHeal()
	{
		throw new NotImplementedException();
	}

	public virtual void Knock()
	{
		if (!Collections.NetworkManager.IsServerMode) return;

		Standing = false;
		// Additional knock logic here
	}

	#endregion

	#region Inventory Implementation

	public virtual void AddItem(IInventoryItem item)
	{
		throw new NotImplementedException();
	}

	public virtual void RemoveItem(string itemId)
	{
		throw new NotImplementedException();
	}

	public virtual void UseItem(string itemId)
	{
		throw new NotImplementedException();
	}

	public virtual void HandleInventorySync(IInventorySyncData state)
	{
		throw new NotImplementedException();
	}

	#endregion
}