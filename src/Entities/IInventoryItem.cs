namespace Staga.Entities;

public interface IInventoryItem
{
    enum ItemType
    {
        Item,
        Skill,
        Spell
    }

    ItemType Type { get; }
    string ItemId { get; }
    
    /// <summary>
    /// Sends a use intent message to the server
    /// </summary>
    void Use();
    
    /// <summary>
    /// Handles the item use on the server side
    /// </summary>
    void ServerUse();
    
    /// <summary>
    /// Handles the item use on the client side
    /// </summary>
    void ClientUse();
    
    /// <summary>
    /// Checks if the player can use this item
    /// </summary>
    /// <returns>True if the player can use the item</returns>
    bool CanPlayerUse();

    /// <summary>
    /// [MessageHandler((ushort)NetworkManager.MessageId.InventoryUseIntent)]
    /// Handles the use intent message from a client
    /// </summary>
    void OnUseIntent();
}
