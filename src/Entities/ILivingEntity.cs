

namespace Staga.Entities;

public interface ILivingEntity : INetworkEntity
{
	bool Standing { get; set; }
	uint Health { get; set; }

	uint BaseMaxHealth { get; init; }
	uint MaxHealth { get; set; }
	
	/// <summary>
	/// Applies damage to the entity with an optional attacker entity. 
	/// </summary>
	/// <param name="amount">The amount of damage to deal</param>
	/// <param name="sourceEntityId">Optional attacker entity</param>
	void ApplyDamage(int amount, uint? sourceEntityId);

	/// <summary>
	/// Applies healing to the entity
	/// </summary>
	/// <param name="amount">Amount to heal</param>
	/// <param name="bypassLimit">Bypasses the max health limit</param>
	void ApplyHealing(int amount, bool bypassLimit);
	
	/// <summary>
	/// Called when the entity takes damage
	/// </summary>
	void OnDamage();
	
	/// <summary>
	/// Called when the entity is healed
	/// </summary>
	void OnHeal();
}
