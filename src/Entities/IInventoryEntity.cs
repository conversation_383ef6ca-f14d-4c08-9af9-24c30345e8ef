using System.Collections.Generic;

namespace Staga.Entities;

public interface IInventoryEntity : INetworkEntity
{
	List<IInventoryItem> Backpack { get; }
	void AddItem(IInventoryItem item);
	void RemoveItem(string itemId);
	void UseItem(string itemId);
	// Sync related events can use explicit DTOs
	void HandleInventorySync(IInventorySyncData state);
}

/// <summary>
/// Defines basic inventory operations that can be performed on an inventory.
/// </summary>
public interface IInventoryOperations
{
    /// <summary>
    /// Adds an item to the inventory.
    /// </summary>
    /// <param name="item">The item to add to the inventory.</param>
    void AddItem(IInventoryItem item);

    /// <summary>
    /// Removes an item from the inventory.
    /// </summary>
    /// <param name="itemId">The ID of the item to remove.</param>
    void RemoveItem(string itemId);

    /// <summary>
    /// Uses an item from the inventory.
    /// </summary>
    /// <param name="itemId">The ID of the item to use.</param>
    void UseItem(string itemId);
}

/// <summary>
/// Defines methods for serializing and deserializing inventory data for network synchronization.
/// </summary>
public interface IInventoryNetworkSync
{
    /// <summary>
    /// Serializes the inventory data into a byte array for network transmission.
    /// </summary>
    /// <returns>A byte array containing the serialized inventory data.</returns>
    byte[] SerializeInventory();

    /// <summary>
    /// Deserializes inventory data from a byte array received over the network.
    /// </summary>
    /// <param name="data">The byte array containing serialized inventory data.</param>
    void DeserializeInventory(byte[] data);
}

public interface IInventorySyncData
{
	/// <summary>
	/// The ID of the entity this inventory belongs to.
	/// </summary>
	uint NetworkId { get; set; }

	/// <summary>
	/// The list of item data currently in the inventory.
	/// </summary>
	List<IInventoryItemData> Items { get; set; }

	/// <summary>
	/// The index or ID of the currently active item.
	/// </summary>
	int? ActiveItemIndex { get; set; }

	/// <summary>
	/// Serializes the inventory data to a byte array for sending over the network.
	/// </summary>
	byte[] Serialize();

	/// <summary>
	/// Deserializes a byte array into an inventory sync data structure.
	/// </summary>
	/// <param name="data">The byte array representing the sync data.</param>
	void Deserialize(byte[] data);
}

public interface IInventoryItemData
{
	/// <summary>
	/// Unique ID of the item type.
	/// </summary>
	string ItemId { get; set; }

	/// <summary>
	/// Optional unique instance ID (for stackables, durability, etc.).
	/// </summary>
	string InstanceId { get; set; }

	/// <summary>
	/// Quantity of the item (if stackable).
	/// </summary>
	int Quantity { get; set; }

	/// <summary>
	/// Serializes the item into a compact format.
	/// </summary>
	byte[] Serialize();

	/// <summary>
	/// Rebuilds the item from a byte buffer.
	/// </summary>
	void Deserialize(byte[] data);
}