namespace Staga.Entities;

/// <summary>
/// Base interface for all network-synchronized entities
/// </summary>
public interface INetworkEntity
{
    /// <summary>
    /// Unique identifier for this entity across the network
    /// </summary>
    uint NetworkId { get; set; }
    
    /// <summary>
    /// ID of the player/client that owns this entity
    /// </summary>
    long OwnerId { get; set; }
    
    /// <summary>
    /// Read-only property indicating whether this entity is owned by the local player/client. Always true on the server.
    /// </summary>
    bool IsOwner { get; }
    
    /// <summary>
    /// Called once after instantiation regardless of client or server
    /// </summary>
    void OnCreate();

    /// <summary>
    /// Called before freeing the entity
    /// </summary>
    void OnDestroy();

    /// <summary>
    /// Serializes the entity's current state into a byte array
    /// </summary>
    /// <returns>Byte array containing the serialized state</returns>
    byte[] SerializeState();

    /// <summary>
    /// Reconstructs the entity's state from a serialized byte array
    /// </summary>
    /// <param name="data">Byte array containing the serialized state</param>
    void DeserializeState(byte[] data);

    /// <summary>
    /// Validates state data received from a client before applying (server-side)
    /// </summary>
    /// <param name="data">The client's proposed state data</param>
    /// <returns>True if the state is valid and can be applied</returns>
    bool ValidateState(byte[] data);
    
    /// <summary>
    /// Handles server-specific updates, typically physics or game logic
    /// </summary>
    /// <param name="delta">Time elapsed since last update in seconds</param>
    void ServerUpdate(double delta);
    
    /// <summary>
    /// Handles client-specific updates for interpolation/prediction
    /// </summary>
    /// <param name="delta">Time elapsed since last update in seconds</param>
    void ClientUpdate(double delta);
    
    /// <summary>
    /// Processes network events received from a client (server-side)
    /// </summary>
    /// <param name="eventType">Type identifier for the event</param>
    /// <param name="args">Additional arguments for the event</param>
    void ProcessClientEvent(byte eventType, byte[] args);

    /// <summary>
    /// Processes network events received from the server (client-side)
    /// </summary>
    /// <param name="eventType">Type identifier for the event</param>
    /// <param name="args">Additional arguments for the event</param>
    void ProcessServerEvent(byte eventType, byte[] args);
}