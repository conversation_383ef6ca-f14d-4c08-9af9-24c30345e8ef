using System;
using Godot;
using System.Collections.Generic;
using System.Linq;
using K4os.Compression.LZ4;
using Staga.Network;
using Lidgren.Network;
using Staga.Entities;
using Staga.Entities.EntityPrefabs;
using Staga.Entities.Humanoid;
using Staga.Entities.Volume;
using Staga.Inventory;
using Staga.Inventory.Items;

namespace Staga.Scenes;

/// <summary>
/// Manages the game world, including entities, networking, and game state synchronization.
/// </summary>
public partial class GameWorld : Node3D
{
	/// <summary>Container node for all entities in the game world</summary>
	public Node EntityContainer { get; private set; }

	/// <summary>Dictionary of all network entities indexed by their network ID</summary>
	public Dictionary<uint, INetworkEntity> Entities { get; } = new();

	/// <summary>Dictionary of all players indexed by their network ID</summary>
	public Dictionary<uint, Player> Players { get; } = new();

	/// <summary>Dictionary of last known states for each entity</summary>
	private readonly Dictionary<uint, byte[]> _lastStates = new();

	/// <summary>Queue for storing entity blacklist until acknowledged</summary>
	private readonly List<uint> _awaitingCorrectAcks = new();
	
	/// <summary>Queue for storing entity state update tries while awaiting the correction ack</summary>
	private readonly Dictionary<uint, uint> _triesWhileAwaitingAck = new();

	/// <summary>Queue for storing entity state updates received from clients</summary>
	private readonly Queue<EntityQueueEntry> _entityUpdateQueue = new();

	private CanvasLayer _multiplayerUi;
	private CanvasLayer _gameWorldDebug;
	private Label _stats;

	public bool Initialized { private set; get; }
	private static uint _nextNetId = 1;

	public override void _Ready()
	{
		try
		{
			EntityContainer = GetNode<Node>("Live");
			_gameWorldDebug = GetNode<CanvasLayer>("GameWorldDebug");
			_stats = _gameWorldDebug.GetNode<Label>("VBoxContainer/stats");

			_gameWorldDebug.Visible = false;

			if (Collections.NetworkManager.IsServerMode)
				InitializeServer();
			else if (Collections.NetworkManager.IsClientMode)
				InitializeClient();
			else
				throw new Exception("GameWorld initialized with no valid network mode");
		}
		catch (Exception e)
		{
			Debug.Crash($"Failed to initialize GameWorld!\n{e}");
		}

		Collections.CurrentGameWorld = this;
		Initialized = true;
	}

	/// <summary>Initializes client-specific components</summary>
	private async void InitializeClient()
	{
		_multiplayerUi = GD.Load<PackedScene>("uid://54xvaciov6cd").Instantiate<CanvasLayer>();
		AddChild(_multiplayerUi);
		MoveChild(_multiplayerUi, 0);

		await Collections.Util.Condition(() => Collections.NetworkManager.Client.ServerMeta != null, 31_556_952f);
		
		// Send ready signal.
		var msg = Collections.NetworkManager.Client.NetClient.CreateMessage();
		msg.Write((byte)NetworkManager.MessageId.Ready);
		Collections.NetworkManager.Client.NetClient.SendMessage(msg, NetDeliveryMethod.ReliableUnordered);
	}

	/// <summary>Initializes server-specific components (currently empty)</summary>
	private void InitializeServer()
	{
		_gameWorldDebug.Visible = true;

		RegionSpawner[] regionSpawners = FindChildren("*", nameof(RegionSpawner)).Cast<RegionSpawner>().ToArray();
		foreach (var spawner in regionSpawners)
		{
			var region = CreateEntity<RegionVolume>();
			region.Position = spawner.Position;
			region.Region = spawner.Region;

			spawner.QueueFree();
		}

		AddChild(GD.Load<PackedScene>("uid://cnxgncvxhnseo").Instantiate());
	}

	public override void _ExitTree()
	{
		Debug.Log("Shutting down the session.");

		if (Collections.NetworkManager.IsServerMode)
			Collections.NetworkManager.Server.StopSession();

		foreach (var id in Entities.Keys.ToList())
			DestroyEntity(id);

		Entities.Clear();
		Players.Clear();
	}

	public override void _PhysicsProcess(double delta)
	{
		if (!Initialized) return;

		if (Collections.NetworkManager.IsServerMode)
			ServerTick(delta);
		else
			ClientTick(delta);
	}

	#region Entity Management

	/// <summary>
	/// Generates a unique network ID for new entities
	/// </summary>
	/// <returns>A unique network ID</returns>
	private uint GenerateUniqueId()
	{
		for (int i = 0; i < 1000; i++)
		{
			if (Collections.NetworkManager.IsClientMode)
				return 0;

			if (!Entities.ContainsKey(_nextNetId))
				return _nextNetId++;

			_nextNetId++;
			if (_nextNetId == 0) _nextNetId = 1; // avoid 0
		}

		Debug.Crash("Failed to generate unique NetworkId");
		return 0;
	}

	/// <summary>
	/// Creates a new network entity of specified type
	/// </summary>
	/// <typeparam name="T">Entity type that implements INetworkEntity</typeparam>
	/// <param name="ownerId">Network ID of entity owner</param>
	/// <returns>Created entity instance</returns>
	public T CreateEntity<T>(long ownerId = 0) where T : Node, INetworkEntity
	{
		var scene = GD.Load<PackedScene>($"res://prefabs/entities/{typeof(T).Name}.tscn");
		var entity = scene.Instantiate<T>();

		entity.NetworkId = GenerateUniqueId();
		entity.OwnerId = ownerId;

		EntityContainer.AddChild(entity);
		entity.OnCreate();

		if (Collections.NetworkManager.IsServerMode)
		{
			Entities[entity.NetworkId] = entity;
			if (entity is Player p) Players[entity.NetworkId] = p;

			BroadcastEntityCreate(entity, typeof(T).Name);
		}

		return entity;
	}

	/// <summary>
	/// Destroys an entity with the specified network ID
	/// </summary>
	/// <param name="id">Network ID of entity to destroy</param>
	public void DestroyEntity(uint id)
	{
		if (!Entities.TryGetValue(id, out var entity))
			return;

		entity.OnDestroy();
		if (entity is Player) Players.Remove(id);
		Entities.Remove(id);

		if (Collections.NetworkManager.IsServerMode)
			BroadcastEntityDestroy(id);

		if (entity is Node node) node.QueueFree();
	}

	#endregion

	#region Networking – Server Update

	/// <summary>
	/// Processes server-side updates and broadcasts entity states
	/// </summary>
	private void ServerTick(double delta)
	{
		_stats.Text =
			$"{1 / delta:0.000} fps\n" +
			$"{Collections.NetworkManager.TickRate} tr\n" +
			$"{Entities.Count} ents\n" +
			$"{_entityUpdateQueue.Count} qeupds\n" +
			$"{Collections.NetworkManager.Server.NetServer.Statistics.SentMessages} msgs\n" +
			$"{Collections.NetworkManager.Server.NetServer.Statistics.DroppedMessages} drops\n" +
			$"{Collections.NetworkManager.Server.NetServer.Statistics.SentBytes} sbytes\n" +
			$"{Collections.NetworkManager.Server.NetServer.Statistics.ReceivedBytes} rbytes";

		foreach (var entity in Entities.Values)
			entity.ServerUpdate(delta);

		while (_entityUpdateQueue.Count > 0)
		{
			var queueEntry = _entityUpdateQueue.Dequeue();
			var entity = Entities[queueEntry.NetworkId];
			entity.DeserializeState(queueEntry.State);
		}

		foreach (var entity in Entities.Values)
			SendEntityState(entity);
	}

	/// <summary>
	/// Sends entity state to all connected clients except the owner
	/// </summary>
	private void SendEntityState(INetworkEntity entity, bool excludeOwner = true)
	{
		byte[] state = entity.SerializeState();
		if (state == null) return;

		byte[] compressed = CompressBytes(state);
		byte[] lastState;

		if (!_lastStates.TryGetValue(entity.NetworkId, out lastState))
			lastState = [];

		if (compressed.Equals(lastState))
			return; // no need to resend as nothing changed.

		var msg = Collections.NetworkManager.Server.NetServer.CreateMessage();
		msg.Write((byte)NetworkManager.MessageId.EntityState);

		msg.Write(entity.NetworkId);
		msg.Write(entity.GetType().Name);
		msg.Write(compressed.Length);
		msg.Write(compressed);

		NetConnection[] exclude = [];
		if (entity.OwnerId != 0 && excludeOwner) // OwnerId of 0 is the server.
			exclude = [Collections.NetworkManager.Server.GetConnection(entity.OwnerId)];

		_lastStates[entity.NetworkId] = compressed;

		Collections.NetworkManager.Server.SendToAll(msg, NetDeliveryMethod.UnreliableSequenced, exclude);
	}

	/// <summary>
	/// Broadcasts entity creation to all connected clients
	/// </summary>
	private void BroadcastEntityCreate(INetworkEntity entity, string type)
	{
		var msg = Collections.NetworkManager.Server.NetServer.CreateMessage();
		msg.Write((byte)NetworkManager.MessageId.EntityCreate);
		msg.Write(type);
		msg.Write(entity.NetworkId);
		msg.Write(entity.OwnerId);

		Collections.NetworkManager.Server.SendToAll(msg, NetDeliveryMethod.ReliableUnordered);
	}

	/// <summary>
	/// Broadcasts entity destruction to all connected clients
	/// </summary>
	private void BroadcastEntityDestroy(uint id)
	{
		var msg = Collections.NetworkManager.Server.NetServer.CreateMessage();
		msg.Write((byte)NetworkManager.MessageId.EntityDestroy);
		msg.Write(id);

		Collections.NetworkManager.Server.SendToAll(msg, NetDeliveryMethod.ReliableOrdered, queueForUnready: true);
	}

	#endregion

	#region Networking – Client Update

	/// <summary>
	/// Processes client-side updates and sends owned entity states to server
	/// </summary>
	private void ClientTick(double delta)
	{
		foreach (var entity in Entities.Values)
			entity.ClientUpdate(delta);

		var client = Collections.NetworkManager.Client.NetClient;

		foreach (var entity in Entities.Values.Where(e => e.IsOwner))
		{
			var msg = client.CreateMessage();
			msg.Write((byte)NetworkManager.MessageId.EntityState);

			var state = entity.SerializeState();
			if (state == null) continue;

			var compressed = CompressBytes(state);
			byte[] lastState;

			if (!_lastStates.TryGetValue(entity.NetworkId, out lastState))
				lastState = [];

			if (compressed.Equals(lastState))
				return; // no need to resend data.

			_lastStates[entity.NetworkId] = compressed;

			msg.Write(entity.NetworkId);
			msg.Write(entity.GetType().Name);
			msg.Write(compressed.Length);
			msg.Write(compressed);

			client.SendMessage(msg, NetDeliveryMethod.UnreliableSequenced);
		}
	}

	#endregion

	#region Message Handlers

	/// <summary>
	/// Routes network messages to appropriate handlers
	/// </summary>
	public void HandleEntityMessage(NetworkManager.MessageId messageId, NetIncomingMessage msg)
	{
		switch (messageId)
		{
			case NetworkManager.MessageId.EntityState:
				HandleEntityState(msg);
				break;
			case NetworkManager.MessageId.EntityStateCorrection:
				HandleEntityCorrection(msg);
				break;
			case NetworkManager.MessageId.EntityCreate:
				HandleEntityCreate(msg);
				break;
			case NetworkManager.MessageId.EntityDestroy:
				HandleEntityDestroy(msg);
				break;
			case NetworkManager.MessageId.EntityEvent:
				HandleEntityEvent(msg);
				break;
			default:
				Debug.Warn($"Unhandled message type: {messageId}");
				break;
		}
	}

	public void HandlePlayerState(NetworkManager.MessageId messageId, NetIncomingMessage msg)
	{
		// switch ((NetworkManager.MessageId)msg.ReadByte())
		// {
		// 	case NetworkManager.MessageId.PlayerListFullSync:
		// 		HandlePlayerListFullSync(msg);
		// 		break;
		// 	case NetworkManager.MessageId.PlayerJoin:
		// 		HandlePlayerJoin(msg);
		// 		break;
		// 	case NetworkManager.MessageId.PlayerLeave:
		// 		HandlePlayerLeave(msg);
		// 		break;
		// }
	}

	/// <summary>
	/// Handles entity state updates from the network
	/// </summary>
	public void HandleEntityState(NetIncomingMessage msg)
	{
		var id = msg.ReadUInt32();
		var type = msg.ReadString();
		var length = msg.ReadInt32();
		var compressed = msg.ReadBytes(length);

		var data = DecompressBytes(compressed);

		if (!Entities.TryGetValue(id, out var entity))
		{
			Debug.Error($"Received state update for non-existent entity: {id}");
			return;
		}

		if (entity.GetType().Name != type)
		{
			Debug.Error($"Received state update for entity of wrong type: {id} ({type})");
			return;
		}

		if (Collections.NetworkManager.IsServerMode)
		{
			// Already checked for null
			// ReSharper disable once PossibleNullReferenceException
			if (_awaitingCorrectAcks.Contains(id))
			{
				_triesWhileAwaitingAck.TryAdd(id, 0);
				_triesWhileAwaitingAck[id]++;
				
				if (_triesWhileAwaitingAck[id] > 60 * 4)
				{
					Debug.Error(
						$"Received state update for entity {id} from client {NetUtility.ToHexString(msg.SenderConnection.RemoteUniqueIdentifier)} while awaiting correction ack for the 4th second in a row. Kicking.");
					Collections.NetworkManager.Server.Kick(msg.SenderConnection, "Invalid player data");
					
					_triesWhileAwaitingAck.Remove(id);
					_awaitingCorrectAcks.Remove(id);
				}
				return;
			}

			// ditto
			// ReSharper disable once PossibleNullReferenceException
			if (entity.OwnerId != msg.SenderConnection.RemoteUniqueIdentifier)
			{
				Debug.Error(
					$"Received state update from non-owner: {id} owned by {NetUtility.ToHexString(entity.OwnerId)} from {NetUtility.ToHexString(msg.SenderConnection.RemoteUniqueIdentifier)}");
				return;
			}

			if (!entity.ValidateState(data))
			{
				Debug.Error(
					$"Received invalid state update for entity: {id} from {NetUtility.ToHexString(msg.SenderConnection.RemoteUniqueIdentifier)}");

				if (!_awaitingCorrectAcks.Contains(entity.NetworkId))
					_awaitingCorrectAcks.Add(entity.NetworkId);

				int count = _entityUpdateQueue.Count;

				// Discard any queued updates as they are now outdated.
				for (int i = 0; i < count; i++)
				{
					var queueEntry = _entityUpdateQueue.Dequeue();
					if (queueEntry.NetworkId != id)
						_entityUpdateQueue.Enqueue(queueEntry);
				}

				byte[] fallbackState = entity.SerializeState();
				byte[] fallbackCompressed = CompressBytes(fallbackState);

				var correctMsg = Collections.NetworkManager.Server.NetServer.CreateMessage();
				correctMsg.Write((byte)NetworkManager.MessageId.EntityStateCorrection);

				correctMsg.Write(entity.NetworkId);
				correctMsg.Write(entity.GetType().Name);
				correctMsg.Write(fallbackCompressed.Length);
				correctMsg.Write(fallbackCompressed);

				Collections.NetworkManager.Server.NetServer.SendMessage(correctMsg, msg.SenderConnection!,
					NetDeliveryMethod.ReliableUnordered);

				return;
			}

			entity.DeserializeState(data);
			entity.ServerUpdate(0);
		}
		else
		{
			entity.DeserializeState(data);
		}
	}

	public void HandleEntityCorrection(NetIncomingMessage msg)
	{
		var id = msg.ReadUInt32();
		var type = msg.ReadString();
		var length = msg.ReadInt32();
		var compressed = msg.ReadBytes(length);

		var data = DecompressBytes(compressed);

		if (!Entities.TryGetValue(id, out var entity))
		{
			Debug.Error($"Received state update for non-existent entity: {id}");
			return;
		}

		if (entity.GetType().Name != type)
		{
			Debug.Error($"Received state update for entity of wrong type: {id} ({type})");
			return;
		}
		
		entity.DeserializeState(data);
		
		var ack = Collections.NetworkManager.Client.NetClient.CreateMessage();
		ack.Write((byte)NetworkManager.MessageId.EntityStateCorrectionAck);
		ack.Write(id);
		
		Collections.NetworkManager.Client.NetClient.SendMessage(ack, NetDeliveryMethod.ReliableUnordered);
	}
	
	public void HandleCorrectionAck(NetIncomingMessage msg)
	{
		uint id = msg.ReadUInt32();

		if (_awaitingCorrectAcks.Contains(id))
		{
			_triesWhileAwaitingAck[id] = 0;
			_awaitingCorrectAcks.Remove(id);
			return;
		}

		Collections.NetworkManager.Server.Kick(msg.SenderConnection, "Invalid player data");
		Debug.Error($"Received correction ack for non-awaiting entity: {id}");
	}

	public void HandleInventoryMessage(NetworkManager.MessageId messageId, NetIncomingMessage msg)
	{
		switch (messageId)
		{
			case NetworkManager.MessageId.InventorySync:
				HandleInventorySync(msg);
				break;
			case NetworkManager.MessageId.InventoryAdd:
				HandleInventoryAdd(msg);
				break;
			case NetworkManager.MessageId.InventoryRemove:
				HandleInventoryRemove(msg);
				break;
			case NetworkManager.MessageId.InventorySwitchIntent:
				HandleInventorySwitchIntent(msg);
				break;
			case NetworkManager.MessageId.InventoryUseIntent:
				HandleInventoryUseIntent(msg);
				break;
			case NetworkManager.MessageId.InventoryUseResponse:
				HandleInventoryUseResponse(msg);
				break;
			default:
				Debug.Warn($"Unhandled inventory message type: {messageId}");
				break;
		}
	}

	private void HandleInventorySync(NetIncomingMessage msg)
	{
		var dataLength = msg.ReadInt32();
		var data = msg.ReadBytes(dataLength);

		var syncData = new InventorySyncData();
		syncData.Deserialize(data);

		if (!Entities.TryGetValue(syncData.NetworkId, out var entity))
		{
			Debug.Error($"Received inventory sync for non-existent entity: {syncData.NetworkId}");
			return;
		}

		if (entity is IInventoryEntity inventoryEntity)
		{
			inventoryEntity.HandleInventorySync(syncData);
			Debug.Log($"Applied inventory sync for entity {syncData.NetworkId}");
		}
	}

	private void HandleInventoryAdd(NetIncomingMessage msg)
	{
		var entityId = msg.ReadUInt32();
		var itemId = msg.ReadString();

		if (!Entities.TryGetValue(entityId, out var entity))
		{
			Debug.Error($"Received inventory add for non-existent entity: {entityId}");
			return;
		}

		if (entity is IInventoryEntity inventoryEntity)
		{
			var item = CreateItemFromId(itemId);
			if (item != null)
			{
				inventoryEntity.AddItem(item);
				Debug.Log($"Added {itemId} to entity {entityId}");
			}
		}
	}

	private void HandleInventoryRemove(NetIncomingMessage msg)
	{
		var entityId = msg.ReadUInt32();
		var itemId = msg.ReadString();

		if (!Entities.TryGetValue(entityId, out var entity))
		{
			Debug.Error($"Received inventory remove for non-existent entity: {entityId}");
			return;
		}

		if (entity is IInventoryEntity inventoryEntity)
		{
			inventoryEntity.RemoveItem(itemId);
			Debug.Log($"Removed {itemId} from entity {entityId}");
		}
	}

	private void HandleInventorySwitchIntent(NetIncomingMessage msg)
	{
		if (!Collections.NetworkManager.IsServerMode) return;

		var entityId = msg.ReadUInt32();
		var newIndex = msg.ReadInt32();

		if (!Entities.TryGetValue(entityId, out var entity))
		{
			Debug.Error($"Received inventory switch intent for non-existent entity: {entityId}");
			return;
		}

		if (entity is BaseHumanoid humanoid && entity.OwnerId == msg.SenderConnection.RemoteUniqueIdentifier)
		{
			bool success = humanoid.SetActiveItem(newIndex >= 0 ? newIndex : null);

			// Send response
			var response = Collections.NetworkManager.Server.NetServer.CreateMessage();
			response.Write((byte)NetworkManager.MessageId.InventorySwitchResponse);
			response.Write(entityId);
			response.Write(newIndex);
			response.Write(success);

			Collections.NetworkManager.Server.NetServer.SendMessage(response, msg.SenderConnection, NetDeliveryMethod.ReliableOrdered);

			Debug.Log($"Entity {entityId} switched to item index {newIndex}: {success}");
		}
	}

	private void HandleInventoryUseIntent(NetIncomingMessage msg)
	{
		if (!Collections.NetworkManager.IsServerMode) return;

		var entityId = msg.ReadUInt32();
		var itemId = msg.ReadString();

		if (!Entities.TryGetValue(entityId, out var entity))
		{
			Debug.Error($"Received inventory use intent for non-existent entity: {entityId}");
			return;
		}

		if (entity is IInventoryEntity inventoryEntity && entity.OwnerId == msg.SenderConnection.RemoteUniqueIdentifier)
		{
			inventoryEntity.UseItem(itemId);
		}
	}

	private void HandleInventoryUseResponse(NetIncomingMessage msg)
	{
		if (Collections.NetworkManager.IsServerMode) return;

		var entityId = msg.ReadUInt32();
		var itemId = msg.ReadString();
		var success = msg.ReadBoolean();

		if (!Entities.TryGetValue(entityId, out var entity))
		{
			Debug.Error($"Received inventory use response for non-existent entity: {entityId}");
			return;
		}

		if (entity is IInventoryEntity inventoryEntity)
		{
			// Find the item and call ClientUse if successful
			var item = inventoryEntity.Backpack.FirstOrDefault(i => i.ItemId == itemId);
			if (item != null && success)
			{
				item.ClientUse();
			}
		}
	}

	private IInventoryItem CreateItemFromId(string itemId)
	{
		return itemId switch
		{
			"test_sword" => new TestSword(),
			"health_potion" => new HealthPotion(),
			_ => null
		};
	}

	/// <summary>
	/// Handles entity creation messages from the network
	/// </summary>
	public void HandleEntityCreate(NetIncomingMessage msg)
	{
		var type = msg.ReadString();
		var id = msg.ReadUInt32();
		var owner = msg.ReadInt64();

		if (Entities.ContainsKey(id))
		{
			Debug.Error($"Received create message for existing entity: {id}");
			return;
		}

		var scene = GD.Load<PackedScene>($"res://prefabs/entities/{type}.tscn");
		if (scene.Instantiate() is not INetworkEntity entity) return;

		entity.NetworkId = id;
		entity.OwnerId = owner;
		EntityContainer.AddChild(entity as Node);
		entity.OnCreate();

		Entities[id] = entity;
		if (entity is Player p) Players[id] = p;

		Debug.Log($"Created entity {id} of type {type} owned by {owner}");
	}

	/// <summary>
	/// Handles entity destruction messages from the network
	/// </summary>
	public void HandleEntityDestroy(NetIncomingMessage msg)
	{
		var id = msg.ReadUInt32();
		DestroyEntity(id);
	}

	private void HandleEntityEvent(NetIncomingMessage msg)
	{
		var id = msg.ReadUInt32();
		var type = msg.ReadByte();
		var length = msg.ReadInt32();
		var args = msg.ReadBytes(length);

		if (!Entities.TryGetValue(id, out var entity))
		{
			Debug.Error($"Received event for non-existent entity: {id}");
			return;
		}

		if (Collections.NetworkManager.IsServerMode)
			entity.ProcessServerEvent(type, args);
		else
			entity.ProcessClientEvent(type, args);
	}

	public void HandlePlayerConnect(long identifier)
	{
		Debug.Log($"Player connected: {NetUtility.ToHexString(identifier)}");

		CreateEntity<Player>(identifier);
	}

	public void HandlePlayerDisconnect(long identifier)
	{
		Debug.Log($"Player disconnected: {NetUtility.ToHexString(identifier)}");
		Debug.Log(
			$"Destroying {Entities.Count(e => e.Value.OwnerId == identifier)} entities owned by {NetUtility.ToHexString(identifier)}");

		foreach (var id in Entities.Keys.ToList())
		{
			if (Entities[id].OwnerId == identifier)
				DestroyEntity(id);
		}
	}

	#endregion

	#region Utilities

	/// <summary>
	/// Compresses byte array using LZ4 compression
	/// </summary>
	public static byte[] CompressBytes(byte[] data)
	{
		// using var mem = new MemoryStream();
		// using (var compressor =
		//        new System.IO.Compression.DeflateStream(mem, System.IO.Compression.CompressionMode.Compress))
		// 	compressor.Write(data, 0, data.Length);
		// return mem.ToArray();
		return LZ4Pickler.Pickle(data);
	}

	/// <summary>
	/// Decompresses LZ4 compressed byte array
	/// </summary>
	public static byte[] DecompressBytes(byte[] data)
	{
		// using var mem = new MemoryStream(data);
		// using var output = new MemoryStream();
		// using (var compressor =
		//        new System.IO.Compression.DeflateStream(mem, System.IO.Compression.CompressionMode.Decompress))
		// 	compressor.CopyTo(output);
		// return output.ToArray();
		return LZ4Pickler.Unpickle(data);
	}

	public bool EntityOwned(INetworkEntity entity)
	{
		return Collections.NetworkManager.IsServerMode ||
		       entity.OwnerId == Collections.NetworkManager.Client.NetClient.UniqueIdentifier;
	}

	#endregion
}