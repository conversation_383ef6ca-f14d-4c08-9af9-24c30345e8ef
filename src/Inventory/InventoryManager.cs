using System;
using System.Collections.Generic;
using System.Linq;
using Godot;
using Staga.Entities;
using Staga.Entities.Humanoid;
using Staga.Inventory.Items;

namespace Staga.Inventory
{
    /// <summary>
    /// Core inventory management system
    /// </summary>
    public class InventoryManager
    {
        private readonly List<IInventoryItem> _items = new();
        private int? _activeItemIndex = null;
        private readonly IInventoryEntity _owner;
        private Node3D _activeItemModel = null;

        public IReadOnlyList<IInventoryItem> Items => _items.AsReadOnly();
        public int? ActiveItemIndex => _activeItemIndex;
        public IInventoryItem ActiveItem => _activeItemIndex.HasValue && _activeItemIndex.Value < _items.Count 
            ? _items[_activeItemIndex.Value] : null;

        public InventoryManager(IInventoryEntity owner)
        {
            _owner = owner;
        }

        /// <summary>
        /// Adds an item to the inventory
        /// </summary>
        public bool AddItem(IInventoryItem item)
        {
            if (item == null) return false;

            // Set the owner
            if (item is BaseItem baseItem)
                baseItem.SetOwner(_owner);

            // Try to stack with existing items first
            if (item.MaxStackSize > 1)
            {
                var existingItem = _items.FirstOrDefault(i => i.ItemId == item.ItemId);
                if (existingItem != null)
                {
                    // For now, just add as separate item since we don't have quantity tracking in IInventoryItem
                    // In a full implementation, you'd handle stacking here
                }
            }

            _items.Add(item);
            Debug.Log($"Added {item.ItemId} to inventory of entity {_owner.NetworkId}");
            return true;
        }

        /// <summary>
        /// Removes an item by ID
        /// </summary>
        public bool RemoveItem(string itemId)
        {
            var item = _items.FirstOrDefault(i => i.ItemId == itemId);
            if (item == null) return false;

            int index = _items.IndexOf(item);
            _items.Remove(item);

            // Adjust active item index if necessary
            if (_activeItemIndex.HasValue)
            {
                if (_activeItemIndex.Value == index)
                {
                    // Active item was removed
                    SetActiveItem(null);
                }
                else if (_activeItemIndex.Value > index)
                {
                    // Shift active index down
                    _activeItemIndex--;
                }
            }

            Debug.Log($"Removed {itemId} from inventory of entity {_owner.NetworkId}");
            return true;
        }

        /// <summary>
        /// Uses an item by ID
        /// </summary>
        public bool UseItem(string itemId)
        {
            var item = _items.FirstOrDefault(i => i.ItemId == itemId);
            if (item == null) return false;

            item.Use();
            return true;
        }

        /// <summary>
        /// Sets the active item by index
        /// </summary>
        public bool SetActiveItem(int? index)
        {
            if (index.HasValue && (index.Value < 0 || index.Value >= _items.Count))
                return false;

            // Unequip current active item
            if (ActiveItem != null && ActiveItem is BaseItem currentActive)
            {
                currentActive.OnUnequip();
                RemoveActiveItemModel();
            }

            _activeItemIndex = index;

            // Equip new active item
            if (ActiveItem != null && ActiveItem is BaseItem newActive)
            {
                newActive.OnEquip();
                CreateActiveItemModel();
            }

            Debug.Log($"Set active item to index {index} for entity {_owner.NetworkId}");
            return true;
        }

        /// <summary>
        /// Switches to the next item in the inventory
        /// </summary>
        public void SwitchToNextItem()
        {
            if (_items.Count == 0)
            {
                SetActiveItem(null);
                return;
            }

            int nextIndex = _activeItemIndex.HasValue ? (_activeItemIndex.Value + 1) % _items.Count : 0;
            SetActiveItem(nextIndex);
        }

        /// <summary>
        /// Switches to the previous item in the inventory
        /// </summary>
        public void SwitchToPreviousItem()
        {
            if (_items.Count == 0)
            {
                SetActiveItem(null);
                return;
            }

            int prevIndex = _activeItemIndex.HasValue ? 
                (_activeItemIndex.Value - 1 + _items.Count) % _items.Count : _items.Count - 1;
            SetActiveItem(prevIndex);
        }

        /// <summary>
        /// Consumes a quantity of the active item (for consumables)
        /// </summary>
        public bool ConsumeActiveItem(int quantity = 1)
        {
            if (ActiveItem == null) return false;

            // For now, just remove the item entirely
            // In a full implementation, you'd handle quantity properly
            return RemoveItem(ActiveItem.ItemId);
        }

        /// <summary>
        /// Creates inventory sync data for network transmission
        /// </summary>
        public InventorySyncData CreateSyncData()
        {
            var itemDataList = new List<IInventoryItemData>();
            
            foreach (var item in _items)
            {
                if (item is BaseItem baseItem)
                {
                    itemDataList.Add(baseItem.CreateItemData());
                }
                else
                {
                    // Fallback for items that don't inherit from BaseItem
                    itemDataList.Add(new InventoryItemData(item.ItemId));
                }
            }

            return new InventorySyncData(_owner.NetworkId, itemDataList, _activeItemIndex);
        }

        /// <summary>
        /// Applies inventory sync data received from network
        /// </summary>
        public void ApplySyncData(IInventorySyncData syncData)
        {
            // Clear current inventory
            _items.Clear();
            SetActiveItem(null);

            // Recreate items from sync data
            foreach (var itemData in syncData.Items)
            {
                var item = CreateItemFromData(itemData);
                if (item != null)
                {
                    AddItem(item);
                }
            }

            // Set active item
            SetActiveItem(syncData.ActiveItemIndex);

            Debug.Log($"Applied inventory sync for entity {_owner.NetworkId}: {_items.Count} items");
        }

        /// <summary>
        /// Creates an item instance from item data
        /// </summary>
        private IInventoryItem CreateItemFromData(IInventoryItemData itemData)
        {
            return itemData.ItemId switch
            {
                "test_sword" => new TestSword(),
                "health_potion" => new HealthPotion(),
                _ => null
            };
        }

        /// <summary>
        /// Creates a visual model for the active item
        /// </summary>
        private void CreateActiveItemModel()
        {
            if (ActiveItem is not BaseItem baseItem) return;
            if (_owner is not BaseHumanoid humanoid) return;

            _activeItemModel = baseItem.CreateWorldModel();
            if (_activeItemModel != null && humanoid.ItemAnchor != null)
            {
                humanoid.ItemAnchor.AddChild(_activeItemModel);
            }
        }

        /// <summary>
        /// Removes the visual model for the active item
        /// </summary>
        private void RemoveActiveItemModel()
        {
            if (_activeItemModel != null)
            {
                _activeItemModel.QueueFree();
                _activeItemModel = null;
            }
        }
    }
}
