using System;
using System.Collections.Generic;
using System.IO;
using Staga.Entities;

namespace Staga.Inventory
{
    /// <summary>
    /// Implementation of IInventorySyncData for network synchronization of inventory state
    /// </summary>
    public class InventorySyncData : IInventorySyncData
    {
        public uint NetworkId { get; set; }
        public List<IInventoryItemData> Items { get; set; } = new();
        public int? ActiveItemIndex { get; set; }

        public InventorySyncData()
        {
        }

        public InventorySyncData(uint networkId, List<IInventoryItemData> items, int? activeItemIndex = null)
        {
            NetworkId = networkId;
            Items = items;
            ActiveItemIndex = activeItemIndex;
        }

        public byte[] Serialize()
        {
            using var memory = new MemoryStream();
            using var writer = new BinaryWriter(memory);

            writer.Write(NetworkId);
            writer.Write(Items.Count);

            foreach (var item in Items)
            {
                var itemData = item.Serialize();
                writer.Write(itemData.Length);
                writer.Write(itemData);
            }

            writer.Write(ActiveItemIndex.HasValue);
            if (ActiveItemIndex.HasValue)
                writer.Write(ActiveItemIndex.Value);

            return memory.ToArray();
        }

        public void Deserialize(byte[] data)
        {
            using var memory = new MemoryStream(data);
            using var reader = new BinaryReader(memory);

            NetworkId = reader.ReadUInt32();
            int itemCount = reader.ReadInt32();

            Items.Clear();
            for (int i = 0; i < itemCount; i++)
            {
                int itemDataLength = reader.ReadInt32();
                byte[] itemData = reader.ReadBytes(itemDataLength);
                
                var item = new InventoryItemData();
                item.Deserialize(itemData);
                Items.Add(item);
            }

            bool hasActiveItem = reader.ReadBoolean();
            ActiveItemIndex = hasActiveItem ? reader.ReadInt32() : null;
        }

        public override string ToString()
        {
            return $"InventorySyncData: Entity {NetworkId}, {Items.Count} items, Active: {ActiveItemIndex}";
        }
    }
}
