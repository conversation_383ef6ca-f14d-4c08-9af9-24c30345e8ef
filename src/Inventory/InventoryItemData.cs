using System;
using System.IO;
using Staga.Entities;

namespace Staga.Inventory
{
    /// <summary>
    /// Implementation of IInventoryItemData for network serialization of inventory items
    /// </summary>
    public class InventoryItemData : IInventoryItemData
    {
        public string ItemId { get; set; } = string.Empty;
        public string InstanceId { get; set; } = string.Empty;
        public int Quantity { get; set; } = 1;

        public InventoryItemData()
        {
        }

        public InventoryItemData(string itemId, int quantity = 1, string instanceId = "")
        {
            ItemId = itemId;
            Quantity = quantity;
            InstanceId = instanceId;
        }

        public byte[] Serialize()
        {
            using var memory = new MemoryStream();
            using var writer = new BinaryWriter(memory);

            writer.Write(ItemId);
            writer.Write(InstanceId);
            writer.Write(Quantity);

            return memory.ToArray();
        }

        public void Deserialize(byte[] data)
        {
            using var memory = new MemoryStream(data);
            using var reader = new BinaryReader(memory);

            ItemId = reader.ReadString();
            InstanceId = reader.ReadString();
            Quantity = reader.ReadInt32();
        }

        public override string ToString()
        {
            return $"{ItemId} x{Quantity} ({InstanceId})";
        }

        public override bool Equals(object obj)
        {
            if (obj is not InventoryItemData other) return false;
            return ItemId == other.ItemId && InstanceId == other.InstanceId;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(ItemId, InstanceId);
        }
    }
}
