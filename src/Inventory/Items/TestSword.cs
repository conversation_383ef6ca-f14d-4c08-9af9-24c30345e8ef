using Godot;
using Staga.Entities;

namespace Staga.Inventory.Items
{
    /// <summary>
    /// Example weapon item - a basic sword
    /// </summary>
    public class TestSword : BaseItem
    {
        public override IInventoryItem.ItemType Type => IInventoryItem.ItemType.Item;
        public override string ItemId => "test_sword";
        public override string DisplayName => "Test Sword";
        public override string Description => "A basic sword for testing the inventory system.";
        public override float UseCooldown => 1.0f; // 1 second cooldown

        private float _lastUseTime = 0f;

        public override void ServerUse()
        {
            if (!Collections.NetworkManager.IsServerMode) return;
            if (Owner is not ILivingEntity livingOwner) return;

            Debug.Log($"Player {Owner.NetworkId} used {DisplayName}");

            // Example: Sword attack logic
            // For now, just heal the player as a test
            livingOwner.ApplyHealing(10, false);
            
            _lastUseTime = Time.GetTicksMsec() / 1000f;
        }

        public override void ClientUse()
        {
            // Client-side visual/audio effects
            Debug.Log($"Client: Using {DisplayName}");
            
            // Play swing sound, animation, etc.
            _lastUseTime = Time.GetTicksMsec() / 1000f;
        }

        public override bool CanPlayerUse()
        {
            if (Owner == null) return false;
            if (Owner is not ILivingEntity livingOwner) return false;
            if (!livingOwner.Standing) return false; // Can't use while knocked down

            float currentTime = Time.GetTicksMsec() / 1000f;
            return currentTime - _lastUseTime >= UseCooldown;
        }

        public override Node3D CreateWorldModel()
        {
            var mesh = new MeshInstance3D();
            var boxMesh = new BoxMesh();
            boxMesh.Size = new Vector3(0.1f, 1.0f, 0.1f); // Sword-like shape
            mesh.Mesh = boxMesh;

            var material = new StandardMaterial3D();
            material.AlbedoColor = Colors.Silver;
            mesh.MaterialOverride = material;

            return mesh;
        }

        public override void OnEquip()
        {
            Debug.Log($"Equipped {DisplayName}");
            // Add sword model to player's hand, etc.
        }

        public override void OnUnequip()
        {
            Debug.Log($"Unequipped {DisplayName}");
            // Remove sword model from player's hand, etc.
        }
    }
}
