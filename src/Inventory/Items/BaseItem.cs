using Godot;
using Lidgren.Network;
using Staga.Entities;
using Staga.Network;

namespace Staga.Inventory.Items
{
    /// <summary>
    /// Base class for all inventory items
    /// </summary>
    public abstract class BaseItem : IInventoryItem
    {
        public abstract IInventoryItem.ItemType Type { get; }
        public abstract string ItemId { get; }
        
        /// <summary>
        /// The entity that owns this item
        /// </summary>
        protected IInventoryEntity Owner { get; private set; }

        /// <summary>
        /// Display name for the item
        /// </summary>
        public abstract string DisplayName { get; }

        /// <summary>
        /// Description of the item
        /// </summary>
        public abstract string Description { get; }

        /// <summary>
        /// Maximum stack size for this item (1 for non-stackable items)
        /// </summary>
        public virtual int MaxStackSize => 1;

        /// <summary>
        /// Whether this item can be used
        /// </summary>
        public virtual bool IsUsable => true;

        /// <summary>
        /// Cooldown time in seconds between uses
        /// </summary>
        public virtual float UseCooldown => 0f;

        public void SetOwner(IInventoryEntity owner)
        {
            Owner = owner;
        }

        public virtual void Use()
        {
            if (!CanPlayerUse()) return;

            // Send use intent to server
            if (Collections.NetworkManager.IsClientMode)
            {
                var msg = Collections.NetworkManager.Client.NetClient.CreateMessage();
                msg.Write((byte)NetworkManager.MessageId.InventoryUseIntent);
                msg.Write(Owner.NetworkId);
                msg.Write(ItemId);
                Collections.NetworkManager.Client.NetClient.SendMessage(msg, NetDeliveryMethod.ReliableOrdered);
            }
            else if (Collections.NetworkManager.IsServerMode)
            {
                // Direct server use
                ServerUse();
            }
        }

        public abstract void ServerUse();
        public abstract void ClientUse();
        public abstract bool CanPlayerUse();

        public virtual void OnUseIntent()
        {
            if (!Collections.NetworkManager.IsServerMode) return;
            if (!CanPlayerUse()) return;

            ServerUse();

            // Send response to client
            var msg = Collections.NetworkManager.Server.NetServer.CreateMessage();
            msg.Write((byte)NetworkManager.MessageId.InventoryUseResponse);
            msg.Write(Owner.NetworkId);
            msg.Write(ItemId);
            msg.Write(true); // Success

            var connection = Collections.NetworkManager.Server.GetConnection(Owner.OwnerId);
            if (connection != null)
                Collections.NetworkManager.Server.NetServer.SendMessage(msg, connection, NetDeliveryMethod.ReliableOrdered);
        }

        /// <summary>
        /// Called when the item becomes the active item
        /// </summary>
        public virtual void OnEquip()
        {
        }

        /// <summary>
        /// Called when the item is no longer the active item
        /// </summary>
        public virtual void OnUnequip()
        {
        }

        /// <summary>
        /// Creates a visual representation of the item for the world
        /// </summary>
        public virtual Node3D CreateWorldModel()
        {
            return null;
        }

        /// <summary>
        /// Creates the item data for network synchronization
        /// </summary>
        public virtual InventoryItemData CreateItemData(int quantity = 1)
        {
            return new InventoryItemData(ItemId, quantity);
        }
    }
}
