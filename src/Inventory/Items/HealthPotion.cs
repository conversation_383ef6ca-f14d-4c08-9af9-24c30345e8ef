using Godot;
using Staga.Entities;
using Staga.Entities.Humanoid;

namespace Staga.Inventory.Items
{
    /// <summary>
    /// Example consumable item - a health potion
    /// </summary>
    public class HealthPotion : BaseItem
    {
        public override IInventoryItem.ItemType Type => IInventoryItem.ItemType.Item;
        public override string ItemId => "health_potion";
        public override string DisplayName => "Health Potion";
        public override string Description => "Restores 50 health points when consumed.";
        public override int MaxStackSize => 10; // Can stack up to 10
        public override float UseCooldown => 2.0f; // 2 second cooldown

        private float _lastUseTime = 0f;
        private const int HealAmount = 50;

        public override void ServerUse()
        {
            if (!Collections.NetworkManager.IsServerMode) return;
            if (Owner is not ILivingEntity livingOwner) return;

            Debug.Log($"Player {Owner.NetworkId} used {DisplayName}");

            // Heal the player
            livingOwner.ApplyHealing(HealAmount, false);
            
            // Remove one potion from inventory (consumable)
            if (Owner is BaseHumanoid humanoid)
            {
                humanoid.ConsumeActiveItem(1);
            }
            
            _lastUseTime = Time.GetTicksMsec() / 1000f;
        }

        public override void ClientUse()
        {
            // Client-side visual/audio effects
            Debug.Log($"Client: Drinking {DisplayName}");
            
            // Play drinking sound, particle effects, etc.
            _lastUseTime = Time.GetTicksMsec() / 1000f;
        }

        public override bool CanPlayerUse()
        {
            if (Owner == null) return false;
            if (Owner is not ILivingEntity livingOwner) return false;
            if (!livingOwner.Standing) return false; // Can't use while knocked down
            if (livingOwner.Health >= livingOwner.MaxHealth) return false; // Already at full health

            float currentTime = Time.GetTicksMsec() / 1000f;
            return currentTime - _lastUseTime >= UseCooldown;
        }

        public override Node3D CreateWorldModel()
        {
            var mesh = new MeshInstance3D();
            var cylinderMesh = new CylinderMesh();
            cylinderMesh.Height = 0.3f;
            cylinderMesh.TopRadius = 0.05f;
            cylinderMesh.BottomRadius = 0.08f;
            mesh.Mesh = cylinderMesh;

            var material = new StandardMaterial3D();
            material.AlbedoColor = Colors.Red;
            material.Transparency = BaseMaterial3D.TransparencyEnum.Alpha;
            material.AlbedoColor = new Color(1, 0, 0, 0.8f); // Semi-transparent red
            mesh.MaterialOverride = material;

            return mesh;
        }

        public override void OnEquip()
        {
            Debug.Log($"Equipped {DisplayName}");
            // Show potion in hand
        }

        public override void OnUnequip()
        {
            Debug.Log($"Unequipped {DisplayName}");
            // Hide potion from hand
        }
    }
}
