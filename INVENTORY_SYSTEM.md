# Inventory System Implementation

This document describes the inventory and active items system that has been implemented for the STAGA game.

## Overview

The inventory system provides:
- **Inventory Management**: Add, remove, and organize items in a player's backpack
- **Active Item System**: Switch between items and use the currently active item
- **Network Synchronization**: Full server-client synchronization of inventory state
- **Item Types**: Support for different item types (weapons, consumables, tools)
- **Server Authority**: All inventory changes are validated server-side

## Architecture

### Core Components

1. **InventoryManager** (`src/Inventory/InventoryManager.cs`)
   - Core inventory management logic
   - Handles item switching, usage, and visual representation
   - Manages active item state

2. **BaseItem** (`src/Inventory/Items/BaseItem.cs`)
   - Base class for all inventory items
   - Implements IInventoryItem interface
   - Handles client-server communication for item usage

3. **Item Implementations**
   - **TestSword** (`src/Inventory/Items/TestSword.cs`): Example weapon item
   - **HealthPotion** (`src/Inventory/Items/HealthPotion.cs`): Example consumable item

4. **Network Data Classes**
   - **InventoryItemData**: Serializable item data for network transmission
   - **InventorySyncData**: Full inventory state synchronization

### Network Messages

The system uses the following network message types (already defined in NetworkManager.cs):

- `InventorySync`: Full inventory synchronization
- `InventoryAdd`: Add item to inventory
- `InventoryRemove`: Remove item from inventory
- `InventorySwitchIntent`: Client request to switch active item
- `InventorySwitchResponse`: Server response to switch request
- `InventoryUseIntent`: Client request to use item
- `InventoryUseResponse`: Server response to use request

## Usage

### Player Controls

When playing as a player character, you can:

- **Switch Items**: Use mouse wheel up/down to cycle through inventory items
- **Use Active Item**: Left-click to use the currently active item
- **Direct Selection**: Use number keys 1-9 to directly select inventory slots (if implemented in input map)

### Adding Items (Server-side)

```csharp
// Add items to a player's inventory
player.AddItem(new TestSword());
player.AddItem(new HealthPotion());

// Set active item
player.SetActiveItem(0); // Set first item as active
```

### Creating New Items

To create a new item type:

1. Create a class inheriting from `BaseItem`
2. Implement required properties and methods
3. Add the item to the factory method in `GameWorld.CreateItemFromId()`

Example:
```csharp
public class MyNewItem : BaseItem
{
    public override IInventoryItem.ItemType Type => IInventoryItem.ItemType.Item;
    public override string ItemId => "my_new_item";
    public override string DisplayName => "My New Item";
    public override string Description => "A custom item for testing.";

    public override void ServerUse()
    {
        // Server-side item usage logic
    }

    public override void ClientUse()
    {
        // Client-side visual/audio effects
    }

    public override bool CanPlayerUse()
    {
        // Usage validation logic
        return true;
    }
}
```

## Testing

The system automatically adds test items to players when they spawn:
- 1x Test Sword (weapon)
- 2x Health Potions (consumables)

The sword is set as the active item by default.

## Implementation Details

### Server Authority

All inventory operations are validated server-side:
- Item usage requires server approval
- Active item switching is confirmed by the server
- Inventory state is synchronized from server to clients

### Visual Representation

Active items are visually represented in the game world:
- Items create 3D models when equipped
- Models are attached to the player's ItemAnchor node
- Models are automatically removed when items are unequipped

### Consumable Items

Consumable items (like health potions) are automatically removed from inventory when used:
- Server validates usage
- Item effect is applied
- Item is removed from inventory
- Inventory sync is sent to clients

## Future Enhancements

Potential improvements to consider:

1. **Item Stacking**: Proper quantity handling for stackable items
2. **Inventory UI**: Visual inventory interface for players
3. **Item Durability**: Degradation system for weapons/tools
4. **Item Trading**: Player-to-player item exchange
5. **Inventory Persistence**: Save/load inventory state
6. **Item Categories**: Better organization of different item types
7. **Hotbar System**: Quick access slots for frequently used items

## Files Modified/Created

### New Files
- `src/Inventory/InventoryManager.cs`
- `src/Inventory/InventoryItemData.cs`
- `src/Inventory/InventorySyncData.cs`
- `src/Inventory/Items/BaseItem.cs`
- `src/Inventory/Items/TestSword.cs`
- `src/Inventory/Items/HealthPotion.cs`

### Modified Files
- `src/Entities/Humanoid/BaseHumanoid.cs`: Implemented inventory functionality
- `src/Entities/Humanoid/Player.cs`: Added inventory input controls and test items
- `src/Scenes/GameWorld.cs`: Added inventory message handling
- `src/Network/ServerManager.cs`: Added inventory sync to initial game state
- `src/Network/ClientManager.cs`: Added client-side inventory message routing

The inventory system is now fully functional and ready for testing and further development!
